<template>
  <bf-dialog
    v-model="dialogVisible"
    ref="keypadDialog"
    :title="$t('dispatch.functionList.keyboardDial')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    modal-class="drag-dialog-modal"
    class="keypad-dialing-dialog"
    width="580px"
    append-to-body
    draggable
    center
  >
    <!-- 拨号显示区域 -->
    <div class="mb-4">
      <bf-input v-model="phoneNumber" placeholder="请输入电话号码" clearable />
    </div>

    <!-- 通话限时设置 -->
    <div class="w-full mb-4">
      <div class="text-white text-sm">{{ $t('dialog.maxSpeakTime') }}：</div>
      <bf-input-number-v2 v-model="callTimeLimit" :min="30" :max="300" :step="10" class="!w-full" />
    </div>

    <!-- 数字键盘 -->
    <div class="keypad-section mb-4">
      <div class="keypad-grid">
        <button v-for="key in keypadKeys" :key="key" class="keypad-button" @click="addDigit(key)">
          {{ key }}
        </button>
      </div>

      <img src="@/assets/images/dispatch/function_list/broadcast_call.svg" class="w-[76px] h-[100px] mt-[20px]"></img>

      <el-tooltip popper-class="bf-tooltip" :content="'切换到联网通话'" placement="bottom">
        <p class="join-net-call-btn"><span class="bf-iconfont bfdx-biaogeguanbi text-[#8299A9]"></span></p>
      </el-tooltip>
    </div>
  </bf-dialog>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import bfDialog from '@/components/bfDialog/main'
  import bfInput from '@/components/bfInput/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'

  // 定义props
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emits
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  // 响应式数据
  const dialogVisible = computed({
    get: () => props.dialogVisible || false,
    set: value => emit('update:dialogVisible', value),
  })
  const phoneNumber = ref('')
  const callTimeLimit = ref(60)

  // 数字键盘布局
  const keypadKeys = computed(() => ['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'])

  // 方法
  const addDigit = (digit: string) => {
    phoneNumber.value += digit
  }
</script>

<style lang="scss">
  .keypad-dialing-dialog.el-dialog.bf-dialog {
    padding: 16px 100px;
    .keypad-section {
      padding: 6px 6px 20px 6px;
      background: rgba(6, 121, 204, 0.46);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      .keypad-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
      }

      .keypad-button {
        background: #065e9a;
        border: 2px solid #94cce8;
        border-radius: 5px;
        width: 115px;
        height: 60px;
        color: #94cce8;
        font-weight: 600;
        font-size: 32px;
        cursor: pointer;

        &:hover {
          background: rgba(148, 204, 232, 0.2);
          border-color: #fff;
        }

        &:active {
          background-color: #FF801D;
          color: #fff;
          border: 2px solid #94cce8;
        }
      }

      .join-net-call-btn {
        width: 50px;
        height: 50px;
        background-color: #053050;
        border-radius: 4px;
        line-height: 50px;
        text-align: center;
        position: absolute;
        margin: 0;
        bottom: 20px;
        right: 6px;
        cursor: pointer;

        span::before {
          font-size: 26px;
          color: #1398E9;
        }

        &:active {
          background-color: #FF801D;
          span::before {
            color: #fff;
          }
        }
      }
    }
  }
</style>
